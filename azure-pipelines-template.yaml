parameters:
  - name: region
    type: string
    default: 'eun'
  - name: platform
    type: string
    default: 'str'
  - name: packageLocation
    type: string
    default: 'drop/PharmaLex.SmartTRACE.Web.zip'
  - name: packageFaLocation
    type: string
    default: 'drop/PharmaLex.SmartTRACE.RemindersApp.zip'
  - name: environments
    type: object

stages:
  - ${{ each env in parameters.Environments }}:
    - stage: '${{ env.name }}'
      displayName: '${{ env.name }} Deploy'
      dependsOn: '${{ env.dependsOn }}'
      variables:
        AppSettings.BuildInfo: 'Build: $(resources.pipeline.build-pipeline.runName)'
        AppSettings.BuildNumber: '$(resources.pipeline.build-pipeline.runName)'
        AppSettings.EnvironmentName: '${{ env.name }}'
        AppSettings.SubmissionRequestStateChangedTemplateId: ${{ env.SubmissionRequestStateChangedTemplateId }}
        AppSettings.ExternalUserLoginEmailTemplateId: ${{ env.ExternalUserLoginEmailTemplateId }}
        AppSettings.ExternalUserSignUpEmailTemplateId: ${{ env.ExternalUserSignUpEmailTemplateId }}
        AppSettings.SubmissionSourceDocumentsUploadedTemplateId: ${{ env.SubmissionSourceDocumentsUploadedTemplateId }}
        AppSettings.SubmissionReminderNotificationTemplateId: ${{ env.SubmissionReminderNotificationTemplateId }}
        ${{ if or(eq(env.prefix, 'prod'), eq(env.prefix, 'dr')) }}:
          AppSettings.AppUrl: 'https://trace.smartphlex.com/submissions/view/'
          AppSettings.SmartTraceConfigurableEmail: '<EMAIL>'
        ${{ else }}:
          AppSettings.AppUrl: 'https://trace-${{ env.name }}.smartphlex.com/submissions/view/'
        AzureStorage.Account: '${{ parameters.platform }}${{ env.prefix }}shared${{ parameters.region }}'
        AzureStorage.PubAccount: '${{ parameters.platform }}${{ env.prefix }}public${{ parameters.region }}'
        AzureStorage.Url: 'https://${{ parameters.platform }}${{ env.prefix }}shared${{ parameters.region }}.blob.core.windows.net/smarttrace'
        ConnectionStrings.default: '$(ConnectionStrings--default)'
        ${{ if eq(env.name, 'prodeu') }}:
          DataFactoryPipeline.Subscription: '3f163d89-70bd-4493-9065-28ece1b2a4bd'
        DataFactoryPipeline.ResourceGroupName: 'rg-${{ parameters.platform }}-${{ env.prefix }}-${{ parameters.region }}'
        DataFactoryPipeline.FactoryName: '${{ parameters.platform }}-${{ env.prefix }}-adf-${{ parameters.region }}'
        KeyVaultName: '${{ parameters.platform }}-${{ env.prefix }}-kv-${{ parameters.region }}'
        Static.Env: '${{ env.name }}'
        ${{ if eq(env.prefix, 'prod') }}: #Axon.Core.AuthenticationType
          AxonCoreAuthentication.Audience: 'https://trace.smartphlex.com/'
          AxonCoreAuthentication.Issuer: 'https://app-eu.smartphlex.com'
          AxonCoreAuthentication.SmartPhlexHostUrl: 'https://app-eu.smartphlex.com'
          AxonCoreAuthentication.SessionTimeoutInMinutes: 15
        ${{ elseif eq(env.prefix, 'dr') }}:
          AxonCoreAuthentication.Audience: 'https://trace-dr.smartphlex.com/'
          AxonCoreAuthentication.Issuer: 'https://app-eu.smartphlex.com'
          AxonCoreAuthentication.SmartPhlexHostUrl: 'https://app-eu.smartphlex.com'
        ${{ elseif eq(env.prefix, 'stg') }}:
          AxonCoreAuthentication.Audience: 'https://trace-stg.smartphlex.com/'
          AxonCoreAuthentication.Issuer: 'https://app-staging.smartphlex.com'
          AxonCoreAuthentication.SmartPhlexHostUrl: 'https://app-staging.smartphlex.com'
          AxonCoreAuthentication.SessionTimeoutInMinutes: 15
        ${{ else }}:
          AxonCoreAuthentication.Audience: 'https://trace-dev.smartphlex.com/'
          AxonCoreAuthentication.Issuer: 'https://app-dev.smartphlex.com'
          AxonCoreAuthentication.SmartPhlexHostUrl: 'https://app-dev.smartphlex.com'
      jobs:
        - deployment: DeploySQL
          displayName: 'Deploy SQL dacpac ${{ env.name }}'
          environment: ${{ env.name }}
          pool: 'pv-windows-pool'
          strategy:
            runOnce:
              deploy:
                steps:
                  - task: DownloadPipelineArtifact@2
                    inputs:
                      source: 'specific'
                      project: $(resources.pipeline.build-pipeline.projectID)
                      pipeline: $(resources.pipeline.build-pipeline.pipelineName)
                      runVersion: 'specific'
                      runBranch: $(resources.pipeline.build-pipeline.sourceBranch)
                      runId: $(resources.pipeline.build-pipeline.runID)
                      path: '$(System.DefaultWorkingDirectory)'
                  - task: SqlAzureDacpacDeployment@1
                    displayName: 'Azure SQL SqlTask'
                    inputs:
                      azureSubscription: ${{ env.azureSubscription }}
                      AuthenticationType: servicePrincipal
                      ServerName: '${{ parameters.platform }}-${{ env.prefix }}-sqlserver-${{ parameters.region }}.database.windows.net'
                      DatabaseName: '${{ parameters.platform }}-${{ env.prefix }}-default-${{ parameters.region }}'
                      deployType: SqlTask
                      SqlFile: '$(System.DefaultWorkingDirectory)/drop/Migrations/migration.sql'
                      IpDetectionMethod: 'AutoDetect'
                      DeleteFirewallRule: true
        - deployment: Deploy
          displayName: 'Deploy Env: ${{ env.name }}'
          environment: ${{ env.name }}
          strategy:
            runOnce:
              deploy:
                steps:
                  - task: DownloadPipelineArtifact@2
                    inputs:
                      source: 'specific'
                      project: $(resources.pipeline.build-pipeline.projectID)
                      pipeline: $(resources.pipeline.build-pipeline.pipelineName)
                      runVersion: 'specific'
                      runBranch: $(resources.pipeline.build-pipeline.sourceBranch)
                      runId: $(resources.pipeline.build-pipeline.runID)
                      path: '$(System.DefaultWorkingDirectory)'

                  - task: AzureKeyVault@2
                    displayName: 'Azure Key Vault: ${{ variables.keyVaultName }}'
                    inputs:
                      azureSubscription: ${{ env.azureSubscription }}
                      KeyVaultName: ${{ variables.keyVaultName }}
                      RunAsPreJob: true

                  - task: FileTransform@1
                    displayName: 'File Transform: web app'
                    inputs:
                      folderPath: '$(System.DefaultWorkingDirectory)/${{ parameters.packageLocation }}'
                      fileType: json
                      targetFiles: '**/appsettings.json'

                  - task: FileTransform@1
                    displayName: 'File Transform: function app'
                    inputs:
                      folderPath: '$(System.DefaultWorkingDirectory)/${{ parameters.packageFaLocation }}'
                      fileType: json
                      targetFiles: '**/appsettings.json'

                  - task: AzureRmWebAppDeployment@4
                    displayName: Azure App Service Deploy
                    inputs:
                      ConnectionType: 'AzureRM'
                      azureSubscription: ${{ env.azureSubscription }}
                      ResourceGroupName: 'rg-${{ parameters.platform }}-${{ env.prefix }}-${{ parameters.region }}'
                      appType: 'webApp'
                      WebAppName: '${{ parameters.platform }}-${{ env.prefix }}-as-${{ parameters.region }}'
                      packageForLinux: '$(System.DefaultWorkingDirectory)/${{ parameters.packageLocation }}'

                  - task: AzureFunctionApp@2
                    displayName: 'Azure Function App Deploy'
                    inputs:
                      connectedServiceNameARM: ${{ env.azureSubscription }}
                      appType: functionApp
                      appName: '${{ parameters.platform }}-${{ env.prefix }}-fa-${{ parameters.region }}'
                      package: '$(System.DefaultWorkingDirectory)/${{ parameters.packageFaLocation }}'
