﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<IncludeOpenAPIAnalyzers>true</IncludeOpenAPIAnalyzers>
		<Product>SmartTrace</Product>
		<Description>$([System.DateTime]::UtcNow.ToString("yyyy-MM-dd"))</Description>
		<Copyright>Copyright © $([System.DateTime]::UtcNow.Year) Cencora</Copyright>
		<GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Axon.Core.Authentication" Version="********" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.1" />
    <PackageReference Include="Azure.Identity" Version="1.14.2" />
    <PackageReference Include="Azure.ResourceManager.DataFactory" Version="1.1.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="8.0.6" />
    <PackageReference Include="NewRelic.Agent" Version="10.27.0" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.2" />
	  <PackageReference Include="PharmaLex.Caching" Version="8.0.0.202" />
	  <PackageReference Include="PharmaLex.Caching.Data" Version="8.0.0.202" />
	  <PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
  </ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="wwwroot\**\*" />
		<Content Remove="wwwroot\fonts\smartphlex-20201119.eot" />
		<Content Remove="wwwroot\fonts\smartphlex-20201119.svg" />
		<Content Remove="wwwroot\fonts\smartphlex-20201119.ttf" />
		<Content Remove="wwwroot\fonts\smartphlex-20201119.woff" />
		<Content Remove="wwwroot\fonts\smartphlex-20201119.woff2" />
		<Content Remove="wwwroot\images\Smartphlex_background_green.png" />
		<Content Remove="wwwroot\images\SmartTrace_logo.png" />
		<Content Remove="wwwroot\js\vue\plx-map.js" />
		<Content Remove="wwwroot\lib\vuex\4.0.0\vuex.global.js" />
		<Content Remove="wwwroot\lib\vue\3.2.6\vue.prod.js" />
		<EmbeddedResource Remove="wwwroot\images\doubler-ring-loader.gif" />
		<EmbeddedResource Remove="wwwroot\images\spinner.gif" />
		<None Remove="wwwroot\fonts\materialdesignicons-webfont.eot" />
		<None Remove="wwwroot\fonts\materialdesignicons-webfont.ttf" />
		<None Remove="wwwroot\fonts\materialdesignicons-webfont.woff" />
		<None Remove="wwwroot\fonts\materialdesignicons-webfont.woff2" />
		<ProjectReference Include="..\PharmaLex.SmartTRACE.Data\PharmaLex.SmartTRACE.Data.csproj" />
		<ProjectReference Include="..\PharmaLex.SmartTRACE.Entities\PharmaLex.SmartTRACE.Entities.csproj" />
		<ProjectReference Include="..\PharmaLex.SmartTRACE.Web.Helpers\PharmaLex.SmartTRACE.Web.Helpers.csproj" />
		<ProjectReference Include="..\PharmaLex.SmartTRACE.Web.Models\PharmaLex.SmartTRACE.Web.Models.csproj" />
		<Content Update="**\*.cshtml" Pack="false" />
		<Content Update="Views\Home\Anonymous.cshtml">
			<Pack>false</Pack>
		</Content>
	</ItemGroup>

  <ItemGroup>
    <Folder Include="newrelic\" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="wwwroot\images\Smartphlex_background_green.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Update="wwwroot\images\SmartTrace_logo.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Update="wwwroot\lib\vuex\4.0.0\vuex.global.js">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

	<PropertyGroup>
		<AddRazorSupportForMvc>true</AddRazorSupportForMvc>
		<IncludeSourceRevisionInInformationalVersion>false</IncludeSourceRevisionInInformationalVersion>
		<Version>2.0.2</Version>
		<Nullable>enable</Nullable>
	</PropertyGroup>
</Project>
