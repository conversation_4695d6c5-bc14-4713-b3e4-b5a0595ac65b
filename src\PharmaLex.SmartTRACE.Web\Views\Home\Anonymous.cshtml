﻿@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration
@{
    ViewData["Title"] = "";
    var smartPhlexLoginUrl = Configuration.GetValue<string>("AxonCoreAuthentication:SmartPhlexHostUrl");
    var tenant = Configuration.GetValue<string>("AxonCoreAuthentication:Tenant");
    var appCodeName = Configuration.GetValue<string>("AxonCoreAuthentication:AppCodeName");
    var postLoginRedirect = ViewData["PostLoginRedirect"] ?? "/";
    var axonLoginUrl = $"{smartPhlexLoginUrl}/{tenant}/externallogin/{appCodeName}?postLoginRedirect={postLoginRedirect}";
}

<div class="login-container">
    <div class="login-box">
        <div class="app-title">
            <span><span class="app-title-regular">SMART</span>TRACE</span>
        </div>
        <div class="login-prompt-container">
            <span class="login-prompt">
                Log in to your Cencora PharmaLex account
            </span>
        </div>
        <a id="plx-login" href="/login?postLoginRedirect=@(postLoginRedirect)" class="button">Login</a>
        <div>
            <h5>Internal Cencora users ONLY</h5>
            <a id="external-login" href="@axonLoginUrl" class="button">Login with SmartPhlex</a>
        </div>
        <div class="logo-container">
            <img src="@Cdn.GetUrl("images/Cencora_PharmaLex_logo_small.png")" alt="Cencora PharmaLex logo">
        </div>
        <div id="warning" class="login-info hidden">
                <span class="browser-warning-icon"></span>
                <p>This system is optimised for use with modern browsers and you are currently using an older browser which is not supported.</p>
                <p>Please use an alternative browser to use this system such as the latest version of Chrome, Firefox, Edge or Safari to continue.</p>
        </div>
    </div>
</div>

@section Scripts {
    <script type="text/javascript">
        var plx = {
            init() {
                document.getElementById('page-header').style.display = 'none';

                try {
                    eval('var x = () => { return true; }');
                } catch (e) {
                    document.getElementById('login').style.display = 'none';
                    document.getElementById('contact').style.display = 'none';
                    document.getElementById('warning').style.display = 'block';
                    document.getElementById('plx-login').style.display = 'none';
                }
            }
        };
        (function () { plx.init(); })();
    </script>
}
