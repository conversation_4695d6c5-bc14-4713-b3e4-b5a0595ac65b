﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Authentication
{
    public class AddClaimsCallback : ITicketReceivedCallback
    {
        public async Task OnTicketReceived(TicketReceivedContext context)
        {

            var graphService = context.HttpContext.RequestServices.GetService<IAzureAdGraphService>();
            ClaimsIdentity ci = context.Principal.Identity as ClaimsIdentity;

            if (ci == null)
            {
                throw new InvalidOperationException("Invalid Claims Principal");
            }

            var isPharmalexUser = await graphService.UserExists(ci.GetClaimValue(ClaimConstants.Email));

            if(isPharmalexUser)
            {
                ci.AddClaim(new System.Security.Claims.Claim("plx:usertype", "pharmalex"));
            }

            var dbContextResolver = context.HttpContext.RequestServices.GetService<IPlxDbContextResolver>();
            var repoFactory = new RepositoryFactory(dbContextResolver, ci.GetEmail());
            ITrackingRepository<User> ur = repoFactory.CreateTracking<User>();

            User u = await ur.Configure(o => o
                                .Include(x => x.UserClaim)
                                    .ThenInclude(x => x.Claim))
                                        .FirstOrDefaultAsync(x => x.Email.ToLower() == ci.GetEmail());
            if (u == null || u.UserClaim.Count == 0)
            {
                if (!context.Principal.IsPharmaLexUser())
                {
                    context.Response.Redirect("/unauthorised");
                    context.HandleResponse();
                    return;
                }

                if (!await ur.Configure(o => o).AnyAsync(c => c.Email.ToLower() == ci.GetEmail()))
                {
                    u = new User
                    {
                        GivenName = ci.GetClaimValue(ClaimConstants.GivenName),
                        FamilyName = ci.GetClaimValue(ClaimConstants.FamilyName),
                        Email = ci.GetEmail(),
                        LastLoginDate = DateTime.Now
                    };
                    ur.Add(u);
                }
            }
            else
            {
                foreach (UserClaim uc in u.UserClaim)
                {
                    ci.AddClaim(new System.Security.Claims.Claim($"{uc.Claim.ClaimType}:{uc.Claim.Name}", uc.ClaimId.ToString()));
                }

                u.LastLoginDate = DateTime.Now;
            }

            await ur.SaveChangesAsync();
            await context.HttpContext.RequestServices.GetService<IDistributedCacheService>().InvalidateAsync("User", "UserClaim", "UserClaim.Claim");
            ci.AddClaim(new System.Security.Claims.Claim("plx:userid", u?.Id.ToString()));
        }
    }
}
