﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.SmartTRACE.Web.HealthCheck;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Hosting;
using System.Net;
using Microsoft.AspNetCore.TestHost;
using PharmaLex.Helpers;
using Newtonsoft.Json;

namespace PharmaLex.SmartTRACE.WebTests.HealthCheck
{
    public class HealthCheckExtensionsTests
    {
        readonly IHost host;
        public HealthCheckExtensionsTests()
        {
            var configuration = TestHelpers.GetConfiguration();

            host = new HostBuilder().ConfigureWebHostDefaults(
                builder => builder
                .UseTestServer()
                .Configure(app => {
                    app.UseRouting();
                    app.UseEndpoints(endpoints =>
                    {
                        endpoints.MapCustomHealthChecks();
                    });
                })
                .ConfigureServices(
                    services => {
                        services.AddSingleton(configuration);
                        services.AddSingleton(sp =>
                            new AppSettingsHelper(sp.GetRequiredService<IConfiguration>()));
                        services.AddOptions();
                        services.AddLogging();
                        services.ConfigureHealthChecks(configuration);

                    })
            ).Build();

        }

        [Fact]
        public async Task Check_ConfigureServices()
        {
            using (host)
            {
                await host.StartAsync();
                var server = host.GetTestServer();
                var client = server.CreateClient();
                var response = await client.GetAsync("/health");

                Assert.Equal(HttpStatusCode.OK, response.StatusCode);
                var content = await response.Content.ReadAsStringAsync();
                var healthResult = JsonConvert.DeserializeObject<HealthResult>(content);

                Assert.Equal("testhost", healthResult!.App);
                Assert.Equal("Healthy", healthResult.Status);
                Assert.Equal("Build:local", healthResult.Version);
            }
        }
    }
}
