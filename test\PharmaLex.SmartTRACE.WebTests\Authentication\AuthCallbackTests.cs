﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using NSubstitute;
using PharmaLex.Caching;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Authentication;
using System.Security.Claims;
using Claim = System.Security.Claims.Claim;

namespace PharmaLex.SmartTRACE.WebTests.Authentication;

public class AuthCallbackTests
{
    private readonly IServiceProvider provider;
    private readonly SmartTRACEContext dbContext;

    public AuthCallbackTests()
    {
        var cacheService = Substitute.For<IDistributedCacheService>();
        var dbctxReslover = TestHelpers.GetPlxDbContextResolver();

        #region Fake Data
        dbContext = (SmartTRACEContext)dbctxReslover.Context;
        if (!dbContext.User.Any(x => x.Id == 1 || x.Id == 2))
        {
            dbContext.User.AddRange(new List<User> {
            new User { Id = 1, Email = "<EMAIL>", CreatedBy = "support", LastUpdatedBy = "support" },
            new User { Id = 2, Email = "<EMAIL>", CreatedBy = "support", LastUpdatedBy = "support" }
            });
        }
        if (!dbContext.Claim.Any(x => x.Id == 1))
            dbContext.Claim.Add(new Entities.Claim { Id = 1, Name = "SystemAdmin", ClaimType = "RegulatoryLead", CreatedBy = "support", LastUpdatedBy = "support" });
        if (!dbContext.UserClaim.Any(x => x.UserId == 1 && x.ClaimId == 1))
            dbContext.UserClaim.Add(new UserClaim { UserId = 1, ClaimId = 1, CreatedBy = "support", LastUpdatedBy = "support" });
        dbContext.SaveChanges();
        #endregion

        provider = Substitute.For<IServiceProvider>();
        provider.GetService(typeof(IPlxDbContextResolver)).Returns(dbctxReslover);
        provider.GetService(typeof(IDistributedCacheService)).Returns(cacheService);

    }

    [Theory]
    [InlineData("<EMAIL>", true, false)]
    [InlineData("<EMAIL>", true, true)]
    [InlineData("<EMAIL>", false, false)]
    [InlineData("<EMAIL>", false, true)]
    [InlineData("<EMAIL>", true, true, false)]
    public async Task Check_OnAuthReceived(string email, bool ispharmalex, bool isInternalDomainUser, bool existingUser = true)
    {
        const string name = "John Doe";

        var context = Substitute.For<HttpContext>();
        context.RequestServices.Returns(provider);
        if (!ispharmalex && !isInternalDomainUser)
        {
            context.Response.Headers.Location.Returns(new StringValues("/unauthorised"));
        }

        var claimIdentity = new ClaimsIdentity(new List<Claim>
        {
            new("emails", email),
            new("name", name),
            new("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress", email)
        });

        if (ispharmalex)
        {
            claimIdentity.AddClaim(new Claim("http://schemas.microsoft.com/identity/claims/identityprovider",
                "https://login.microsoftonline.com/ff9ac3ce-3c41-41c3-b556-e1b32a662fed/v2.0"));
        }

        context.User = new ClaimsPrincipal(claimIdentity);

        var sut = new AuthCallback();
        var task = sut.OnAuthReceived(context.User, context);
        await task;
        if (!ispharmalex && !isInternalDomainUser)
        {
            Assert.Equal("/unauthorised", context.Response.Headers.Location);
        }
        else
        {
            var dbUser = dbContext.User.FirstOrDefault(u => u.Email == email);
            Assert.NotNull(dbUser);
            Assert.True(dbUser.LastLoginDate.HasValue);
            Assert.Equal(DateTime.Now.Date, dbUser.LastLoginDate.Value.Date);

            if (!existingUser)
            {
                Assert.Equal("John", dbUser.GivenName);
                Assert.Equal("Doe", dbUser.FamilyName);
                Assert.Equal(DateTime.Now.Date, dbUser.CreatedDate.Date);
            }
        }
        Assert.True(task.IsCompletedSuccessfully);
    }

    [Fact]
    public async Task Check_OnAuthReceived_nullClaimsIdentity()
    {
        var context = new DefaultHttpContext();
        context.RequestServices = provider;
        context.User = new ClaimsPrincipal();

        var sut = new AuthCallback();
        // Act
        var ex = await Assert.ThrowsAsync<InvalidOperationException>(() => sut.OnAuthReceived(context.User, context));
        // Assert
        Assert.Equal("Invalid Claims Principal", ex.Message);
    }
}
