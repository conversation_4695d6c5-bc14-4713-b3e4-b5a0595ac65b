﻿using Microsoft.AspNetCore.Mvc;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Models;
using NSubstitute;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using System.Security.Claims;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authentication;
using Claim = System.Security.Claims.Claim;

namespace PharmaLex.SmartTRACE.WebTests.Controllers;

public class HomeControllerTests
{
    private readonly IConfiguration configurationMock;
    private readonly HomeController homeController;
    public HomeControllerTests()
    {
        configurationMock = new ConfigurationBuilder()
           .AddInMemoryCollection(
           [
               new KeyValuePair<string, string?>("SendGrid", "test-Key"),
               new KeyValuePair<string, string?>("AppSettings:SystemAdminEmail","<EMAIL>"),
               new KeyValuePair<string, string?>("emailsSendingEnabled","true"),
               new KeyValuePair<string, string?>("Static:App","YourApp")

           ])
           .Build();
        homeController = new HomeController(configurationMock);
    }

    [Fact]
    public void Login_nullPath_ThrowsException()
    {
        // Arrange          
        string? path = null;
        // Create the substitute
        var urlHelper = Substitute.For<IUrlHelper>();
        urlHelper.IsLocalUrl(Arg.Any<string>()).Returns(false); 
        homeController.Url = urlHelper;
        // Assert
        var ex = Assert.Throws<InvalidOperationException>(() => homeController.Login(path));
        Assert.Equal("Access denied. This URL is not on the approved domains list.", ex.Message);
    }

    [Theory]
    [InlineData("login")]
    public void Login_WithValidPath_RedirectsToPath(string path)
    {
        // Arrange
        var urlHelper = Substitute.For<IUrlHelper>();
        urlHelper.IsLocalUrl(Arg.Any<string>()).Returns(true);
        homeController.Url = urlHelper;
        // Act
        var result = homeController.Login(path);
        // Assert
        var localRedirectResult = Assert.IsType<LocalRedirectResult>(result);
        Assert.Equal($"/{path?.Trim('/')}", localRedirectResult.Url);
    }

    [Theory]
    [InlineData("")]
    public void Login_WithInvalidPath_ThrowsException(string path)
    {
        // Create the substitute
        var urlHelper = Substitute.For<IUrlHelper>();
        urlHelper.IsLocalUrl(Arg.Any<string>()).Returns(false);
        homeController.Url = urlHelper;
        // Assert
        var ex = Assert.Throws<InvalidOperationException>(() => homeController.Login(path));
        Assert.Equal("Access denied. This URL is not on the approved domains list.", ex.Message);
    }

    [Theory]
    [InlineData("")]
    [InlineData("login")]
    public void Login_InvalidValidPath_(string path)
    {
        homeController.ModelState.AddModelError("Key", "ErrorMessage");
        // Act
        var result = homeController.Login(path) as BadRequestObjectResult;
        // Assert
        Assert.NotNull(result);
        Assert.Equal(400, result.StatusCode);
    }
    [Theory]
    [InlineData("loginn")]
    public void Login_WithValidPath_RedirectsError(string path)
    {
        // Arrange
        var urlHelper = Substitute.For<IUrlHelper>();
        urlHelper.IsLocalUrl(Arg.Any<string>()).Returns(true);
        homeController.Url = urlHelper;
        // Act
        var ex = Assert.Throws<InvalidOperationException>(() => homeController.Login(path));
        // Assert
        Assert.Equal("Access denied. This URL is not on the approved domains list.", ex.Message);
    }

    [Theory]
    [InlineData("test.")]
    [InlineData("test test")]
    [InlineData(":\\test")]
    [InlineData("test//test")]
    public void Login_WithInvalidPath_ThrowsUriFormatException(string path)
    {
        // Arrange
        var urlHelper = Substitute.For<IUrlHelper>();
        urlHelper.IsLocalUrl(Arg.Any<string>()).Returns(true);
        homeController.Url = urlHelper;
        // Act
        var ex = Assert.Throws<UriFormatException>(() => homeController.Login(path));
        // Assert
        Assert.Equal("Invalid URL path.", ex.Message);
    }

    [Fact]
    public void Error_ReturnsViewWithCorrectModel()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.TraceIdentifier = "TestId";
        homeController.ControllerContext = new ControllerContext { HttpContext = context };
        // Act
        var result = homeController.Error() as ViewResult;
        // Assert
        Assert.NotNull(result);
        Assert.IsType<ViewResult>(result);
        var model = result.Model as ErrorViewModel;
        var hasRequestid = model?.ShowRequestId;
        Assert.Equal(!string.IsNullOrEmpty(model?.RequestId), hasRequestid);
        Assert.NotNull(model);
        Assert.Equal("TestId", model.RequestId);
    }

    [Fact]
    public async Task Logout_SignsUserOutAndRedirects()
    {
        // Arrange
        var mockHttpContext =  Substitute.For<HttpContext>();
        var authServiceMock = Substitute.For<IAuthenticationService>();
        authServiceMock.SignOutAsync(Arg.Any<HttpContext>(), Arg.Any<string>(), Arg.Any<AuthenticationProperties>()).Returns(Task.FromResult<object?>(null));
        var serviceProviderMock = Substitute.For<IServiceProvider>();
        serviceProviderMock.GetService(typeof(IAuthenticationService)).Returns(authServiceMock);
        mockHttpContext.RequestServices.Returns(serviceProviderMock);

        homeController.ControllerContext = new ControllerContext { HttpContext = mockHttpContext };
        //Act
        var task = homeController.Logout();
        var result = await task;
        // Assert
        var signOutResult = Assert.IsType<SignOutResult>(result);
        Assert.NotNull(signOutResult.Properties);
        Assert.Equal("/", signOutResult.Properties.RedirectUri);
        Assert.Equal(new[] { CookieAuthenticationDefaults.AuthenticationScheme, OpenIdConnectDefaults.AuthenticationScheme }, signOutResult.AuthenticationSchemes);
    }

    #region index
    [Fact]
    public void Index_Returns_View_When_User_Is_Authenticated()
    {
        var configuration = new ConfigurationBuilder()
           .AddInMemoryCollection(new Dictionary<string, string>
           {
              { "AppSettings:HomeScreen", "Home" }
           }.Select(kv => new KeyValuePair<string, string?>(kv.Key, kv.Value)))
          .Build();
        var controller = new HomeController(configuration);
        var identity = new ClaimsIdentity(new Claim[]
       {
          new Claim(ClaimTypes.Name, "testUser")
       }, "TestAuthentication");
        var user = new ClaimsPrincipal(identity);
        controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = user }
        };
        var result = controller.Index();
        // Assert
        var viewResult = Assert.IsType<RedirectResult>(result);
        Assert.NotNull(result);
        var expectedResult = "home";
        Assert.Equal(expectedResult, viewResult.Url);
    }

    [Theory]
    [InlineData("/data/2")]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("/")]
    public void Index_Returns_Anonymous_View_When_User_Is_Not_Authenticated(string? postLoginRedirect)
    {
        var user = Substitute.For<ClaimsPrincipal>();
        user?.Identity?.IsAuthenticated.Returns(false);
        var httpContext = new DefaultHttpContext();
        httpContext.User = user!;
        var controllerContext = new ControllerContext
        {
            HttpContext = httpContext
        };
        var configuration = Substitute.For<IConfiguration>();
        var controller = new HomeController(configuration)
        {
            ControllerContext = controllerContext
        };
        // Act
        var result = controller.Index(postLoginRedirect);
        // Assert            
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(result);
        Assert.Equal("Anonymous", viewResult.ViewName);
        Assert.True(controller.ViewData.ContainsKey("PostLoginRedirect"));
        Assert.True(controller.ViewData["PostLoginRedirect"]?.Equals(string.IsNullOrEmpty(postLoginRedirect) ? "/" : postLoginRedirect));
    }
    [Fact]
    public void Index_Returns_View_User_Is_Not_Authenticated_EmptyHomeRedirect()
    {
        var configuration = new ConfigurationBuilder()
         .AddInMemoryCollection(new Dictionary<string, string>
         {
            { "AppSettings:HomeScreen", " " }
         }.Select(kv => new KeyValuePair<string, string?>(kv.Key, kv.Value)))
         .Build();
        var controller = new HomeController(configuration);
        var identity = new ClaimsIdentity(new Claim[]
       {
          new Claim(ClaimTypes.Name, "testUser")
       }, "TestAuthentication");
        var user = new ClaimsPrincipal(identity);
        controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = user }
        };
        var result = controller.Index();
        // Assert            
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(result);
    }
    #endregion

    [Fact]
    public void Unauthorised_Returns_Anonymous_View_With_Unauthorised_ViewData()
    {
        // Arrange           
        var expectedViewName = "Anonymous";
        homeController.TempData = Substitute.For<ITempDataDictionary>();
        homeController.ViewData["HomeKey"] = "HomeValue";
        // Act
        var result = homeController.Unauthorised() as ViewResult;
        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedViewName, result.ViewName);
        Assert.True(homeController.ViewData.ContainsKey("unauthorised"));
    }
}
