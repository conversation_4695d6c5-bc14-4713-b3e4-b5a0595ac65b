parameters:
  - name: packageLocation
    type: string
    default: 'drop/PharmaLex.SmartTRACE.Web.zip'
  - name: packageFaLocation
    type: string
    default: 'drop/PharmaLex.SmartTRACE.RemindersApp.zip'
  - name: environments
    type: object
    default:
      - name: 'dev'
        prefix: 'dev'
        azureSubscription: 'SmartTrace Development'
        SubmissionRequestStateChangedTemplateId: 'd-a41e0dbca59242cc867f386cb387f1ce'
        ExternalUserLoginEmailTemplateId: 'd-a2c0c5021d764ae68ed8af50f52bb048'
        ExternalUserSignUpEmailTemplateId: 'd-a07f826a31ab4b22b086e6e6fd1d18a0'
        SubmissionSourceDocumentsUploadedTemplateId: 'd-e2ed0dde33884c29b0a4023ed15cd25c'
        SubmissionReminderNotificationTemplateId: 'd-cb79e61c0c4241bbbe941339fa646d82'
      - name: 'stg'
        prefix: 'stg'
        azureSubscription: 'SmartTrace Development'
        SubmissionRequestStateChangedTemplateId: 'd-beb2d0ca5a304d73af243458e83d003e'
        ExternalUserLoginEmailTemplateId: 'd-1e66875e819b44f19ef2e5c09790eede'
        ExternalUserSignUpEmailTemplateId: 'd-e2812ed215534b2290cb43d1cd3677a4'
        SubmissionSourceDocumentsUploadedTemplateId: 'd-f24db221a2564f10af0e2d299bb528e7'
        SubmissionReminderNotificationTemplateId: 'd-f5e58e1ff0f04180a861ecb9564a5e61'
        dependsOn: 'dev'
      - name: 'prodeu'
        prefix: 'prod'
        locked: true
        azureSubscription: 'SmartTrace Production'
        SubmissionRequestStateChangedTemplateId: 'd-f8b4cdffde3d4ce8a1cfe5e3ca7d1546'
        ExternalUserLoginEmailTemplateId: 'd-b14e1dd4ee13464e932c51240586c8a7'
        ExternalUserSignUpEmailTemplateId: 'd-8f65a6bbcd0543999a6e0fdd221736ff'
        SubmissionSourceDocumentsUploadedTemplateId: 'd-c3a0983985474ce3a440a7809877e297'
        SubmissionReminderNotificationTemplateId: 'd-70f9876e4d9b4bc29194b7e84f6b3e31'
        dependsOn: 'stg'  

pool: 'pv-pool'

trigger: none

resources:
  pipelines:
    - pipeline: build-pipeline
      source: SmartTrace.Build
      project: SmartTrace
      trigger:
        branches:
          include:
            - develop

stages:
  - template: ./azure-pipelines-template.yaml
    parameters:
      packageLocation: ${{ parameters.packageLocation }}
      packageFaLocation: ${{ parameters.packageFaLocation }}
      environments: ${{ parameters.environments }}