using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace PharmaLex.SmartTRACE.Web.Middleware
{
    /// <summary>
    /// Middleware to enforce a session timeout after the configured minutes.
    /// </summary>
    /// <param name="options">Contains the options for this middleware.</param>
    public class SessionTimeoutMiddleware(IOptions<SessionTimeoutSettings> options) : IMiddleware
    {
        private readonly int _timeoutMinutes = options.Value.TimeoutMinutes;

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            var authResult = await context.AuthenticateAsync(CookieAuthenticationDefaults.AuthenticationScheme);

            if (authResult.Succeeded && authResult.Principal?.Identity?.IsAuthenticated == true)
            {
                var issuedUtc = authResult.Properties.IssuedUtc ?? DateTimeOffset.MinValue;
                var expiresUtc = authResult.Properties.ExpiresUtc ?? DateTimeOffset.UtcNow;
                var timeDiff = expiresUtc - issuedUtc;

                if (timeDiff.TotalMinutes <= _timeoutMinutes)
                {
                    // expiration time was set and is in accordance to max timeout
                    var now = DateTimeOffset.UtcNow;
                    var timeElapsed = now - issuedUtc;

                    if (timeElapsed.TotalMinutes > (_timeoutMinutes / 3.0))
                    {
                        // refresh expiration after one-third of the timeout to enhance user experience, especially for short timeouts
                        authResult.Properties.ExpiresUtc = now.AddMinutes(_timeoutMinutes);
                        authResult.Properties.IssuedUtc = now;
                        await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, authResult.Principal, authResult.Properties);
                    }
                }
                else
                {
                    // If expiration time is too long or not set, set it now
                    authResult.Properties.ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(_timeoutMinutes);
                    authResult.Properties.IssuedUtc = DateTimeOffset.UtcNow;
                    await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, authResult.Principal, authResult.Properties);
                }
            }
            await next(context);
        }
    }
}