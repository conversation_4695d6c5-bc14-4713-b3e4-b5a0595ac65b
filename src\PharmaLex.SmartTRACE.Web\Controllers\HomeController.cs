﻿using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Authentication;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    public partial class HomeController : BaseController
    {
        private readonly IConfiguration configuration;
        private readonly string[] allowedUrls = { "", "login" };
        public HomeController(IConfiguration configuration)
        {
            this.configuration = configuration;
        }

        [HttpGet, AllowAnonymous]
        public IActionResult Index([FromQuery] string? postLoginRedirect = null)
        {
            if (!this.User.Identity.IsAuthenticated)
            {
                ViewData["landing"] = true;
                ViewData["PostLoginRedirect"] = string.IsNullOrEmpty(postLoginRedirect) ? "/" : postLoginRedirect;
                return View("Anonymous");
            }

            string homeRedirect = this.configuration.GetValue<string>("AppSettings:HomeScreen");
            if (!string.IsNullOrWhiteSpace(homeRedirect))
                return this.Redirect(homeRedirect.ToLower());

            return this.View();
        }

        [Route("/login")]
        public IActionResult Login(string path)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            path = (path ?? string.Empty).Trim().Trim('/').Trim();
            if (path.Contains("//") ||
                path.Contains(":\\") ||
                path.Contains('.') ||
                path.Contains(' '))
            {
                throw new UriFormatException("Invalid URL path.");
            }
            if (Url.IsLocalUrl($"/{path}") && allowedUrls.Contains(path))
            {
                return this.LocalRedirect($"/{path}");
            }
            else
            {
                throw new InvalidOperationException("Access denied. This URL is not on the approved domains list.");
            }
        }

        [Route("/logout")]
        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(AxonAuthenticationDefaults.AxonCookieAuthenticationScheme);

            return SignOut(new AuthenticationProperties { RedirectUri = "/" },
                CookieAuthenticationDefaults.AuthenticationScheme, OpenIdConnectDefaults.AuthenticationScheme);
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return this.View(new Models.ErrorViewModel
            {
                RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier,
                NotifyEmail = this.configuration.GetValue<string>("AppSettings:SystemAdminEmail"),
                AppName = this.configuration.GetValue<string>("Static:App")
            });
        }

        [HttpGet("/unauthorised"), AllowAnonymous]
        public IActionResult Unauthorised()
        {
            this.ViewData["unauthorised"] = true;
            return View("Anonymous");
        }
    }
}
