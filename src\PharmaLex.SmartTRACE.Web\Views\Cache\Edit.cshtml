﻿@model CacheViewModel
@{
    ViewData["Title"] = Model.Key + " - Cache Inspect";
}

<div class="manage-container">
    <header class="manage-header">
        <h2>Inspect cache - @Model.Key</h2>
        <a class="button secondary icon-button-back" href="/cache">Cache List</a>
        <a class="button icon-button-delete" id="deleteCache" href="">Delete</a>
        <form id="deleteForm" style="display: none;" method="post" action="/cache/delete">
            @Html.AntiForgeryToken()
            <input type="text" name="key" value="@Model.Key" />
        </form>
    </header>
    <form method="post">
        @Html.AntiForgeryToken()
        <div class="form-col form-col-two-third">
            <textarea asp-for="Value" style="width: 100%; height:500px;"></textarea>
            <input type="hidden" name="Key" value="@Model.Key" />
            <div class="cache-warning">
                <div>Updating cache values is dangerous and can lead to exceptions in the application.</div>
                <div>Do not change the structure of the JSON and always verify the new cache value is a valid JSON string before you update it.</div>
            </div>
        </div>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/cache">Cancel</a>
            <button class="icon-button-save">Save</button>
        </div>
    </form>
</div>

@section Scripts {
    <script type="text/javascript">
        plx.contracts = {
            init() {
                let textEl = document.getElementById('Value');
                textEl.value = JSON.stringify(JSON.parse('@Html.Raw(Model.Value.Replace("'", "&#39;"))'), undefined, 4);

                let deleteCacheButton = document.getElementById('deleteCache');
                document.getElementById('deleteCache').addEventListener('click', function (e) {
                    e.preventDefault();
                    let deleteForm = document.getElementById('deleteForm');
                    deleteForm.submit();
                });
            }
        };
        plx.contracts.init();
    </script>
}

