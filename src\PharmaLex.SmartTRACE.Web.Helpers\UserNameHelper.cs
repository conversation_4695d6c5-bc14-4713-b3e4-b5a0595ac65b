﻿using System;

namespace PharmaLex.SmartTRACE.Web.Helpers;

public static class UserNameHelper
{
    public static string GetGivenName(string name)
    {
        var indexOfFirstSpace = name.IndexOf(' ', StringComparison.Ordinal);
        return indexOfFirstSpace == -1 ? name : name[..indexOfFirstSpace];
    }

    public static string GetFamilyName(string name)
    {
        var indexOfFirstSpace = name.IndexOf(' ', StringComparison.Ordinal);
        return indexOfFirstSpace == -1 ? name : name[(indexOfFirstSpace + 1)..];
    }
}
