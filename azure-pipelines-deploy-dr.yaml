parameters:
  - name: packageLocation
    type: string
    default: 'drop/PharmaLex.SmartTRACE.Web.zip'
  - name: packageFaLocation
    type: string
    default: 'drop/PharmaLex.SmartTRACE.RemindersApp.zip'
  - name: environments
    type: object
    default:
      - name: 'dr'
        prefix: 'dr'
        locked: true
        azureSubscription: 'SmartTrace Production'
        SubmissionRequestStateChangedTemplateId: 'd-f8b4cdffde3d4ce8a1cfe5e3ca7d1546'
        ExternalUserLoginEmailTemplateId: 'd-b14e1dd4ee13464e932c51240586c8a7'
        ExternalUserSignUpEmailTemplateId: 'd-8f65a6bbcd0543999a6e0fdd221736ff'
        SubmissionSourceDocumentsUploadedTemplateId: 'd-c3a0983985474ce3a440a7809877e297'

pool: 'pv-pool'

trigger: none

resources:
  pipelines:
    - pipeline: build-pipeline
      source: SmartTrace.Build
      project: SmartTrace

variables:
  platform: 'str'
  region: 'euw'

stages:
  - template: ./azure-pipelines-template.yaml
    parameters:
      packageLocation: ${{ parameters.packageLocation }}
      packageFaLocation: ${{ parameters.packageFaLocation }}
      environments: ${{ parameters.environments }}
