﻿using System.Security.Claims;

namespace PharmaLex.SmartTRACE.Web.Extensions;

public static class ClaimsIdentityExtensions
{

    public static void AddCompanyClaim(this ClaimsIdentity claimsIdentity, string companyId)
    {
        claimsIdentity.AddClaim(new Claim("plx:companyid", companyId));
    }

    public static void AddUserIsActiveClaim(this ClaimsIdentity claimsIdentity, string isActive)
    {
        claimsIdentity.AddClaim(new Claim("plx:userisactive", isActive.ToLower()));
    }
}
