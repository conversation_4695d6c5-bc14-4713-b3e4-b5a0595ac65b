﻿using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Axon.Core.Authentication.Extensions;
using Axon.Core.Authentication.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.Caching;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Extensions;
using PharmaLex.SmartTRACE.Web.Helpers;

namespace PharmaLex.SmartTRACE.Web.Authentication
{
    public class AuthCallback : IAuthCallback
    {
        public async Task OnAuthReceived(ClaimsPrincipal claimsPrincipal, HttpContext context)
        {
            var claimsIdentity = claimsPrincipal.Identity as ClaimsIdentity;

            if (claimsIdentity == null)
            {
                throw new InvalidOperationException("Invalid Claims Principal");
            }

            var userEmails = claimsIdentity.Claims.Where(c => ClaimsExtensions.UserEmailClaims.Contains(c.Type))
                .Select(c => c.Value.ToLower()).ToList();
            var primaryEmail = claimsPrincipal.GetAxonEmail()?.ToLower();

            var dbContextResolver = context.RequestServices.GetService<IPlxDbContextResolver>();
            var repoFactory = new RepositoryFactory(dbContextResolver, primaryEmail);
            var userRepository = repoFactory.CreateTracking<User>();

            var user = await userRepository.Configure(o => o.Include(x => x.UserClaim)
                                                                .ThenInclude(x => x.Claim))
                                            .FirstOrDefaultAsync(x => userEmails.Contains(x.Email.ToLower()));

            if (user == null)
            {
                if (!claimsPrincipal.IsInternalDomainUser())
                {
                    context.Response.Redirect("/unauthorised");
                    return;
                }

                var fullName = claimsIdentity.Claims.GetClaimValue("name") ?? string.Empty;
                user = new User
                {
                    GivenName = UserNameHelper.GetGivenName(fullName),
                    FamilyName = UserNameHelper.GetFamilyName(fullName),
                    Email = primaryEmail,
                    LastLoginDate = DateTime.Now
                };
                userRepository.Add(user);

                SetUserInactive(claimsIdentity);
            }
            else
            {
                if (user.UserClaim.Count == 0)
                {
                    SetUserInactive(claimsIdentity);
                }

                foreach (var userClaim in user.UserClaim)
                {
                    claimsIdentity.AddClaim(new System.Security.Claims.Claim(
                        $"{userClaim.Claim.ClaimType}:{userClaim.Claim.Name}", userClaim.ClaimId.ToString()));
                }

                user.LastLoginDate = DateTime.Now;
            }

            await userRepository.SaveChangesAsync();
            var distributedCacheService = context.RequestServices.GetService<IDistributedCacheService>();
            if (distributedCacheService != null)
            {
                await distributedCacheService.InvalidateAsync("User", "UserClaim", "UserClaim.Claim");
            }
                    
            claimsIdentity.AddClaim(new System.Security.Claims.Claim("plx:userid", user.Id.ToString()));
        }

        private static void SetUserInactive(ClaimsIdentity claimsIdentity)
        {
            claimsIdentity.AddCompanyClaim("0");
            claimsIdentity.AddUserIsActiveClaim("false");
        }
    }
}