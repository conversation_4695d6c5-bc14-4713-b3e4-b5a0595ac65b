﻿using Axon.Core.Authentication.Extensions;
using Microsoft.AspNetCore.Http;
using PharmaLex.Authentication.B2C;
using PharmaLex.DataAccess;

namespace PharmaLex.SmartTRACE.Web
{
    public class PlxUserContext : IUserContext
    {
        private readonly IHttpContextAccessor httpContextAccessor;

        public PlxUserContext(IHttpContextAccessor httpContextAccessor)
        {
            this.httpContextAccessor = httpContextAccessor;
        }

        public string? User
        {
            get => httpContextAccessor.HttpContext?.User.GetEmail() ??
                   httpContextAccessor.HttpContext?.User.GetAxonEmail();
        }
    }
}
